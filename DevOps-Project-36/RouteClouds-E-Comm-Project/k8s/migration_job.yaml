apiVersion: batch/v1
kind: Job
metadata:
  name: database-migration
  namespace: routeclouds-ns
spec:
  ttlSecondsAfterFinished: 100
  template:
    spec:
      containers:
      - name: migration
        image: awsfreetier30/routeclouds-backend:migration-ready
        imagePullPolicy: Always
        command: ["/bin/bash", "-c"]
        args:
        - |
          echo "Starting database setup with migration-ready image..."
          echo "Available npm scripts:"
          npm run 2>/dev/null || echo "No scripts available"
          echo ""
          echo "Running database migration and seeding..."
          npm run migrate
          npm run seed
          echo "Database setup completed successfully!"
        envFrom:
        - secretRef:
            name: db-secrets
        - configMapRef:
            name: app-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      restartPolicy: Never
      activeDeadlineSeconds: 300
  backoffLimit: 3
